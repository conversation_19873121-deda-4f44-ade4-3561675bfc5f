/** @odoo-module **/

import { Component } from "@odoo/owl";
import { OrderReceipt } from "@point_of_sale/app/screens/receipt_screen/receipt/order_receipt";
import { patch } from "@web/core/utils/patch";

/**
 * Saudi Arabian ZATCA QR Code Generator
 * Implements ZATCA e-invoicing QR code requirements
 */
export class ZATCAQRGenerator {
    
    /**
     * Generate ZATCA-compliant QR code data
     * @param {Object} receiptData - Receipt data from POS
     * @returns {String} Base64 encoded QR data
     */
    static generateQRData(receiptData) {
        try {
            // ZATCA QR Code fields (TLV format)
            const qrData = {
                sellerName: "مؤسسة الحسابات التجارية", // Tag 1: Seller Name
                vatNumber: "300123456700003", // Tag 2: VAT Registration Number
                timestamp: receiptData.date || new Date().toISOString(), // Tag 3: Timestamp
                totalAmount: (receiptData.amount_total || 0).toFixed(2), // Tag 4: Invoice Total
                taxAmount: (receiptData.amount_tax || 0).toFixed(2) // Tag 5: VAT Total
            };
            
            // Create TLV (Tag-Length-Value) encoded string
            const tlvData = this.createTLVString(qrData);
            
            // Convert to Base64 for QR code
            const base64Data = btoa(tlvData);
            
            console.log("ZATCA QR Data generated:", {
                sellerName: qrData.sellerName,
                vatNumber: qrData.vatNumber,
                timestamp: qrData.timestamp,
                totalAmount: qrData.totalAmount,
                taxAmount: qrData.taxAmount,
                base64: base64Data
            });
            
            return base64Data;
            
        } catch (error) {
            console.error("Error generating ZATCA QR code:", error);
            return "";
        }
    }
    
    /**
     * Create TLV (Tag-Length-Value) encoded string for ZATCA
     * @param {Object} data - QR data object
     * @returns {String} TLV encoded string
     */
    static createTLVString(data) {
        let tlvString = "";
        
        // Tag 1: Seller Name (UTF-8 encoded)
        tlvString += this.encodeTLV(1, data.sellerName);
        
        // Tag 2: VAT Registration Number
        tlvString += this.encodeTLV(2, data.vatNumber);
        
        // Tag 3: Timestamp (ISO 8601 format)
        tlvString += this.encodeTLV(3, data.timestamp);
        
        // Tag 4: Invoice Total (with VAT)
        tlvString += this.encodeTLV(4, data.totalAmount);
        
        // Tag 5: VAT Total
        tlvString += this.encodeTLV(5, data.taxAmount);
        
        return tlvString;
    }
    
    /**
     * Encode a single TLV field
     * @param {Number} tag - Tag number
     * @param {String} value - Value to encode
     * @returns {String} TLV encoded field
     */
    static encodeTLV(tag, value) {
        // Convert value to UTF-8 bytes for proper length calculation
        const utf8Value = unescape(encodeURIComponent(value));
        const tagByte = String.fromCharCode(tag);
        const lengthByte = String.fromCharCode(utf8Value.length);
        return tagByte + lengthByte + utf8Value;
    }
}

/**
 * Patch OrderReceipt to include ZATCA QR code generation
 */
patch(OrderReceipt.prototype, {
    
    setup() {
        super.setup();
        this.initializeSaudiReceipt();
    },
    
    /**
     * Initialize Saudi receipt features
     */
    initializeSaudiReceipt() {
        // Generate ZATCA QR code when receipt is rendered
        setTimeout(() => {
            this.updateZATCAQRCode();
        }, 100);
    },
    
    /**
     * Update QR code placeholder with ZATCA data
     */
    updateZATCAQRCode() {
        try {
            const qrPlaceholder = document.querySelector('.sa-qr-code-placeholder');
            if (qrPlaceholder && this.props.data) {
                const qrData = ZATCAQRGenerator.generateQRData(this.props.data);
                
                if (qrData) {
                    // Update placeholder text
                    qrPlaceholder.textContent = `QR:${qrData.substring(0, 8)}...`;
                    qrPlaceholder.title = `ZATCA QR Code Data: ${qrData}`;
                    
                    // Add data attribute for potential QR code libraries
                    qrPlaceholder.setAttribute('data-qr-content', qrData);
                    qrPlaceholder.setAttribute('data-qr-type', 'zatca');
                    
                    console.log("ZATCA QR code updated in receipt");
                }
            }
        } catch (error) {
            console.error("Error updating ZATCA QR code:", error);
        }
    }
});

/**
 * Saudi Receipt Utilities
 */
export class SaudiReceiptUtils {
    
    /**
     * Format Saudi currency
     * @param {Number} amount - Amount to format
     * @returns {String} Formatted currency string
     */
    static formatSaudiCurrency(amount) {
        return `${amount.toFixed(2)} ر.س`;
    }
    
    /**
     * Format Arabic date
     * @param {String|Date} date - Date to format
     * @returns {String} Formatted Arabic date
     */
    static formatArabicDate(date) {
        const dateObj = new Date(date);
        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        
        return dateObj.toLocaleString('ar-SA', options);
    }
    
    /**
     * Validate VAT number format
     * @param {String} vatNumber - VAT number to validate
     * @returns {Boolean} True if valid
     */
    static validateVATNumber(vatNumber) {
        // Saudi VAT number format: 15 digits
        const vatRegex = /^\d{15}$/;
        return vatRegex.test(vatNumber);
    }
    
    /**
     * Validate Commercial Registration number
     * @param {String} crNumber - CR number to validate
     * @returns {Boolean} True if valid
     */
    static validateCRNumber(crNumber) {
        // Saudi CR number format: 10 digits
        const crRegex = /^\d{10}$/;
        return crRegex.test(crNumber);
    }
}

console.log("Saudi Receipt QR module loaded successfully");
