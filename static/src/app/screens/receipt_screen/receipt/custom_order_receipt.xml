<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Saudi Arabian Order Receipt Template -->
    <t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        
        <!-- Apply Saudi thermal receipt class to the main container -->
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="class">pos-receipt sa-thermal-receipt</attribute>
        </xpath>
        
        <!-- Add Saudi invoice type header after the receipt header -->
        <xpath expr="//ReceiptHeader" position="after">
            <!-- Saudi Invoice Type Section -->
            <div class="sa-invoice-type-section">
                <div class="sa-invoice-type">
                    <div class="sa-invoice-type-ar">فاتورة ضريبية مبسطة</div>
                    <div class="sa-invoice-type-en">Simplified Tax Invoice</div>
                </div>
            </div>
            
            <!-- Saudi Invoice Information Section -->
            <div class="sa-invoice-info-section">
                <!-- Invoice Number -->
                <div class="sa-info-row">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">رقم الفاتورة</span>
                        <span class="sa-label-en">Invoice No.</span>
                    </div>
                    <span class="sa-info-value sa-mono">
                        <t t-esc="props.data.name"/>
                    </span>
                </div>
                
                <!-- Date and Time -->
                <div class="sa-info-row">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">التاريخ</span>
                        <span class="sa-label-en">Date</span>
                    </div>
                    <span class="sa-info-value">
                        <t t-esc="props.data.date"/>
                    </span>
                </div>
                
                <!-- Customer Information (if available) -->
                <div class="sa-info-row" t-if="props.data.partner">
                    <div class="sa-info-labels">
                        <span class="sa-label-ar">العميل</span>
                        <span class="sa-label-en">Customer</span>
                    </div>
                    <span class="sa-info-value">
                        <t t-esc="props.data.partner.name"/>
                    </span>
                </div>
            </div>
            
            <!-- Saudi Products Table Header -->
            <div class="sa-products-header">
                <div class="sa-separator-line">================================</div>
                <div class="sa-table-header">
                    <div class="sa-header-row">
                        <span class="sa-col-item">الصنف / Item</span>
                        <span class="sa-col-qty">الكمية / Qty</span>
                        <span class="sa-col-price">السعر / Price</span>
                        <span class="sa-col-total">المجموع / Total</span>
                    </div>
                </div>
                <div class="sa-separator-line">================================</div>
            </div>
        </xpath>
        
        <!-- Enhance the order lines with Saudi formatting -->
        <xpath expr="//OrderWidget" position="attributes">
            <attribute name="class">sa-order-widget</attribute>
        </xpath>
        
        <!-- Replace the total section with Saudi format -->
        <xpath expr="//div[@class='pos-receipt-right-align'][text()='--------']" position="replace">
            <div class="sa-separator-line">================================</div>
        </xpath>
        
        <!-- Enhance the total amount display -->
        <xpath expr="//div[@class='pos-receipt-amount'][1]" position="replace">
            <div class="sa-total-section">
                <!-- Subtotal -->
                <div class="sa-total-row">
                    <span class="sa-total-label-ar">المجموع الفرعي</span>
                    <span class="sa-total-label-en">Subtotal</span>
                    <span class="sa-total-value">
                        <t t-esc="props.formatCurrency(props.data.amount_total)"/>
                    </span>
                </div>
                
                <!-- VAT (if applicable) -->
                <div class="sa-total-row" t-if="props.data.amount_tax">
                    <span class="sa-total-label-ar">ضريبة القيمة المضافة (15%)</span>
                    <span class="sa-total-label-en">VAT (15%)</span>
                    <span class="sa-total-value">
                        <t t-esc="props.formatCurrency(props.data.amount_tax)"/>
                    </span>
                </div>
                
                <!-- Grand Total -->
                <div class="sa-grand-total">
                    <span class="sa-grand-total-label-ar">الإجمالي</span>
                    <span class="sa-grand-total-label-en">TOTAL</span>
                    <span class="sa-grand-total-value">
                        <t t-esc="props.formatCurrency(props.data.amount_total)"/>
                    </span>
                </div>
            </div>
        </xpath>
        
        <!-- Add Saudi footer after payment lines -->
        <xpath expr="//div[@class='after-footer']" position="after">
            <!-- Saudi Thank You Section -->
            <div class="sa-footer-section">
                <div class="sa-separator-line">================================</div>
                
                <!-- Thank You Message -->
                <div class="sa-thank-you">
                    <div class="sa-thank-you-ar">شكراً لزيارتكم</div>
                    <div class="sa-thank-you-en">Thank you for your visit</div>
                </div>
                
                <!-- QR Code Section (if available) -->
                <div class="sa-qr-section" t-if="props.data.qr_code">
                    <div class="sa-qr-instructions">
                        <span class="sa-label-ar">امسح الرمز للفاتورة الإلكترونية</span>
                        <span class="sa-label-en">Scan for electronic invoice</span>
                    </div>
                    <!-- QR Code would be rendered here -->
                </div>
                
                <!-- Store Information -->
                <div class="sa-store-info">
                    <div class="sa-store-slogan-ar">نتطلع لخدمتكم مرة أخرى</div>
                    <div class="sa-store-slogan-en">We look forward to serving you again</div>
                </div>
            </div>
        </xpath>
        
    </t>
</templates>
