<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Saudi Arabian Order Receipt Template -->
    <t t-name="point_of_sale.OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension">
        
        <!-- Apply Saudi thermal receipt class to the main container -->
        <xpath expr="//div[@class='pos-receipt']" position="attributes">
            <attribute name="class">pos-receipt sa-thermal-receipt</attribute>
        </xpath>
        
        <!-- Add Saudi Products Table Header after the receipt header -->
        <xpath expr="//ReceiptHeader" position="after">
            <!-- Saudi Products Table Header -->
            <div class="sa-products-header">
                <div class="sa-separator-line">================================</div>
                <div class="sa-table-header">
                    <div class="sa-header-row">
                        <span class="sa-col-item">المنتج</span>
                        <span class="sa-col-qty">الكمية</span>
                        <span class="sa-col-price">السعر</span>
                        <span class="sa-col-total">المجموع</span>
                    </div>
                    <div class="sa-header-row-en">
                        <span class="sa-col-item-en">Product</span>
                        <span class="sa-col-qty-en">Qty</span>
                        <span class="sa-col-price-en">Price</span>
                        <span class="sa-col-total-en">Total</span>
                    </div>
                </div>
                <div class="sa-separator-line">================================</div>
            </div>
        </xpath>
        
        <!-- Enhance the order lines with Saudi formatting -->
        <xpath expr="//OrderWidget" position="attributes">
            <attribute name="class">sa-order-widget</attribute>
        </xpath>
        
        <!-- Replace the total section with Saudi format -->
        <xpath expr="//div[@class='pos-receipt-right-align'][text()='--------']" position="replace">
            <div class="sa-separator-line">================================</div>
        </xpath>

        <!-- Enhance the total amount display -->
        <xpath expr="//div[@class='pos-receipt-amount'][1]" position="replace">
            <div class="sa-total-section">
                <!-- Subtotal -->
                <div class="sa-total-row">
                    <span class="sa-total-label-ar">المجموع الفرعي : </span>
                    <span class="sa-total-value sa-mono">
                        <t t-esc="props.formatCurrency(props.data.amount_total - (props.data.amount_tax || 0))"/>
                    </span>
                </div>

                <!-- VAT (if applicable) -->
                <div class="sa-total-row" t-if="props.data.amount_tax">
                    <span class="sa-total-label-ar">ضريبة (15%) : </span>
                    <span class="sa-total-value sa-mono">
                        <t t-esc="props.formatCurrency(props.data.amount_tax)"/>
                    </span>
                </div>

                <!-- Grand Total -->
                <div class="sa-grand-total">
                    <span class="sa-grand-total-label-ar">الإجمالي : </span>
                    <span class="sa-grand-total-value sa-mono">
                        <t t-esc="props.formatCurrency(props.data.amount_total)"/>
                    </span>
                </div>

                <!-- Payment Method -->
                <div class="sa-payment-method-row" t-if="props.data.paymentlines and props.data.paymentlines.length > 0">
                    <span class="sa-payment-label-ar">طريقة الدفع : </span>
                    <span class="sa-payment-method-value">
                        <t t-esc="props.data.paymentlines[0].name || 'نقداً'"/>
                    </span>
                </div>
            </div>
        </xpath>
        
        <!-- Add Saudi footer after payment lines -->
        <xpath expr="//div[@class='after-footer']" position="after">
            <!-- Saudi QR Code Section -->
            <div class="sa-qr-section">
                <div class="sa-separator-line">================================</div>

                <!-- QR Code Instructions -->
                <div class="sa-qr-instructions">
                    <div class="sa-qr-label-ar">امسح الرمز للفاتورة الإلكترونية</div>
                    <div class="sa-qr-label-en">Scan for electronic invoice</div>
                </div>

                <!-- ZATCA QR Code -->
                <div class="sa-qr-container">
                    <div class="sa-qr-code-placeholder">
                        <!-- QR Code will be generated with ZATCA data -->
                        <div class="sa-qr-data" style="display: none;">
                            <span class="seller-name">مؤسسة الحسابات التجارية</span>
                            <span class="vat-number">300123456700003</span>
                            <span class="invoice-date"><t t-esc="props.data.date || '2025-08-04T10:30:00Z'"/></span>
                            <span class="total-amount"><t t-esc="props.data.amount_total || '20.70'"/></span>
                            <span class="tax-amount"><t t-esc="props.data.amount_tax || '2.70'"/></span>
                        </div>
                        [QR CODE]
                    </div>
                </div>
            </div>

            <!-- Saudi Thank You Section -->
            <div class="sa-footer-section">
                <div class="sa-separator-line">================================</div>

                <!-- Thank You Message -->
                <div class="sa-thank-you">
                    <div class="sa-thank-you-ar">شكراً لكم</div>
                    <div class="sa-thank-you-en">Thank you!</div>
                </div>
            </div>
        </xpath>
        
    </t>
</templates>
