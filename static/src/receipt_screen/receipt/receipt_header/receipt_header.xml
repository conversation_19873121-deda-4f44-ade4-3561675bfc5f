<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <!-- Saudi Arabian Receipt Header Template - Complete Implementation -->
    <t t-name="point_of_sale.ReceiptHeader">

        <!-- Saudi Bordered Business Information Container -->
        <div class="sa-bordered-business-container">

            <!-- Saudi Business Name Section -->
            <div class="sa-business-name-section">
                <!-- Arabic Business Name (Primary) -->
                <div class="sa-business-name-ar">
                    مؤسسة الحسابات التجارية
                </div>
                <!-- English Business Name (Secondary) -->
                <div class="sa-business-name-en">
                    Al-Hesabat Commercial Establishment
                </div>
            </div>

            <!-- Business Address Section -->
            <div class="sa-address-section">
                <!-- Arabic Address (Primary) -->
                <div class="sa-address-ar">
                    الرياض - شارع العليا
                </div>
                <!-- English Address (Secondary) -->
                <div class="sa-address-en">
                    Riyadh - Al-Olaya Street
                </div>
            </div>

            <!-- Invoice Type Section -->
            <div class="sa-invoice-type-section">
                <div class="sa-invoice-type-ar">فاتورة ضريبية مبسطة</div>
                <div class="sa-invoice-type-en">Simplified Tax Invoice</div>
            </div>

            <!-- Registration Information Section -->
            <div class="sa-registration-section">
                <!-- Commercial Registration Number -->
                <div class="sa-cr-number">
                    <span class="sa-cr-label-ar">السجل التجاري : </span>
                    <span class="sa-cr-value sa-mono">1012345678</span>
                </div>

                <!-- VAT Registration Number -->
                <div class="sa-vat-number">
                    <span class="sa-vat-label-ar">الرقم الضريبي : </span>
                    <span class="sa-vat-value sa-mono">300123456700003</span>
                </div>

                <!-- Phone Number -->
                <div class="sa-phone-number">
                    <span class="sa-phone-label-ar">رقم الهاتف : </span>
                    <span class="sa-phone-value sa-mono">0555555555</span>
                </div>
            </div>

        </div>

        <!-- Transaction Details Section -->
        <div class="sa-transaction-details-section">

            <!-- Invoice Number -->
            <div class="sa-transaction-row">
                <span class="sa-transaction-label-ar">رقم الفاتورة : </span>
                <span class="sa-transaction-value sa-mono">
                    <t t-esc="props.data.name || 'INV20250804-0001'"/>
                </span>
            </div>

            <!-- Date and Time -->
            <div class="sa-transaction-row">
                <span class="sa-transaction-label-ar">التاريخ والوقت : </span>
                <span class="sa-transaction-value sa-mono">
                    <t t-esc="props.data.date || '2025-08-04 10:30 AM'"/>
                </span>
            </div>

            <!-- POS Reference -->
            <div class="sa-transaction-row">
                <span class="sa-transaction-label-ar">رقم الطلب : </span>
                <span class="sa-transaction-value sa-mono">
                    <t t-esc="props.data.pos_reference || 'POS/2025/00123'"/>
                </span>
            </div>

            <!-- Cashier Name -->
            <div class="sa-transaction-row" t-if="props.data.cashier">
                <span class="sa-transaction-label-ar">اسم الكاشير : </span>
                <span class="sa-transaction-value">
                    <t t-esc="props.data.cashier || 'عبد الله أحمد'"/>
                </span>
            </div>

            <!-- Terminal -->
            <div class="sa-transaction-row">
                <span class="sa-transaction-label-ar">رقم النقطة : </span>
                <span class="sa-transaction-value">
                    <t t-esc="props.data.terminal || 'POS Terminal 1'"/>
                </span>
            </div>

        </div>

        <!-- Header Separator -->
        <div class="sa-header-separator"></div>

    </t>
</templates>
