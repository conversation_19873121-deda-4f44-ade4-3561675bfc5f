#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Saudi Arabian Receipt Implementation Test
=========================================

This test file validates the Saudi Arabian receipt implementation
for Odoo POS system with thermal printer optimization.

Test Coverage:
- Receipt header structure
- Arabic RTL text support
- Bordered business information container
- Transaction details formatting
- Products table layout
- Totals section with VAT calculation
- ZATCA QR code implementation
- Thermal printer compatibility (58mm/80mm)

Usage:
    python3 test_saudi_receipt_implementation.py
"""

import os
import sys
import json
from datetime import datetime

class SaudiReceiptTester:
    """Test class for Saudi Arabian receipt implementation"""
    
    def __init__(self):
        self.test_results = []
        self.module_path = os.path.dirname(os.path.abspath(__file__))
        
    def log_test(self, test_name, status, message=""):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status_symbol = "✓" if status == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {message}")
    
    def test_file_structure(self):
        """Test if all required files exist"""
        required_files = [
            "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml",
            "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml",
            "static/src/css/custom_pos_receipts.css",
            "static/src/js/saudi_receipt_qr.js",
            "__manifest__.py"
        ]
        
        for file_path in required_files:
            full_path = os.path.join(self.module_path, file_path)
            if os.path.exists(full_path):
                self.log_test(f"File exists: {file_path}", "PASS")
            else:
                self.log_test(f"File missing: {file_path}", "FAIL", f"File not found: {full_path}")
    
    def test_receipt_header_content(self):
        """Test receipt header template content"""
        header_file = os.path.join(self.module_path, "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml")
        
        if not os.path.exists(header_file):
            self.log_test("Receipt header content", "FAIL", "Header file not found")
            return
        
        with open(header_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test for required Arabic content
        required_elements = [
            "مؤسسة الحسابات التجارية",  # Business name
            "الرياض - شارع العليا",      # Address
            "فاتورة ضريبية مبسطة",      # Invoice type
            "السجل التجاري :",          # Commercial registration
            "الرقم الضريبي :",          # VAT number
            "رقم الهاتف :",             # Phone number
            "sa-bordered-business-container"  # CSS class
        ]
        
        for element in required_elements:
            if element in content:
                self.log_test(f"Header contains: {element}", "PASS")
            else:
                self.log_test(f"Header missing: {element}", "FAIL")
    
    def test_transaction_details(self):
        """Test transaction details section"""
        header_file = os.path.join(self.module_path, "static/src/receipt_screen/receipt/receipt_header/receipt_header.xml")
        
        if not os.path.exists(header_file):
            self.log_test("Transaction details", "FAIL", "Header file not found")
            return
        
        with open(header_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        transaction_elements = [
            "رقم الفاتورة :",    # Invoice number
            "التاريخ والوقت :",  # Date and time
            "رقم الطلب :",      # POS reference
            "اسم الكاشير :",    # Cashier name
            "رقم النقطة :",     # Terminal
            "sa-transaction-details-section"
        ]
        
        for element in transaction_elements:
            if element in content:
                self.log_test(f"Transaction details contains: {element}", "PASS")
            else:
                self.log_test(f"Transaction details missing: {element}", "FAIL")
    
    def test_products_table_structure(self):
        """Test products table structure"""
        receipt_file = os.path.join(self.module_path, "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml")
        
        if not os.path.exists(receipt_file):
            self.log_test("Products table structure", "FAIL", "Receipt file not found")
            return
        
        with open(receipt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        table_elements = [
            "المنتج",           # Product
            "الكمية",           # Quantity
            "السعر",            # Price
            "المجموع",          # Total
            "sa-products-header",
            "sa-header-row",
            "sa-header-row-en"
        ]
        
        for element in table_elements:
            if element in content:
                self.log_test(f"Products table contains: {element}", "PASS")
            else:
                self.log_test(f"Products table missing: {element}", "FAIL")
    
    def test_totals_section(self):
        """Test totals section structure"""
        receipt_file = os.path.join(self.module_path, "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml")
        
        if not os.path.exists(receipt_file):
            self.log_test("Totals section", "FAIL", "Receipt file not found")
            return
        
        with open(receipt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        totals_elements = [
            "المجموع الفرعي :",   # Subtotal
            "ضريبة (15%) :",     # Tax
            "الإجمالي :",        # Total
            "طريقة الدفع :",     # Payment method
            "sa-total-section",
            "sa-grand-total"
        ]
        
        for element in totals_elements:
            if element in content:
                self.log_test(f"Totals section contains: {element}", "PASS")
            else:
                self.log_test(f"Totals section missing: {element}", "FAIL")
    
    def test_qr_code_implementation(self):
        """Test QR code implementation"""
        receipt_file = os.path.join(self.module_path, "static/src/app/screens/receipt_screen/receipt/custom_order_receipt.xml")
        qr_js_file = os.path.join(self.module_path, "static/src/js/saudi_receipt_qr.js")
        
        # Test XML structure
        if os.path.exists(receipt_file):
            with open(receipt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            qr_elements = [
                "sa-qr-section",
                "امسح الرمز للفاتورة الإلكترونية",  # QR instructions
                "sa-qr-code-placeholder",
                "ZATCA"
            ]
            
            for element in qr_elements:
                if element in content:
                    self.log_test(f"QR XML contains: {element}", "PASS")
                else:
                    self.log_test(f"QR XML missing: {element}", "FAIL")
        
        # Test JavaScript implementation
        if os.path.exists(qr_js_file):
            with open(qr_js_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            js_elements = [
                "ZATCAQRGenerator",
                "generateQRData",
                "createTLVString",
                "encodeTLV",
                "مؤسسة الحسابات التجارية",
                "300123456700003"
            ]
            
            for element in js_elements:
                if element in content:
                    self.log_test(f"QR JS contains: {element}", "PASS")
                else:
                    self.log_test(f"QR JS missing: {element}", "FAIL")
    
    def test_css_thermal_optimization(self):
        """Test CSS thermal printer optimization"""
        css_file = os.path.join(self.module_path, "static/src/css/custom_pos_receipts.css")
        
        if not os.path.exists(css_file):
            self.log_test("CSS thermal optimization", "FAIL", "CSS file not found")
            return
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        css_elements = [
            "sa-thermal-receipt",
            "sa-bordered-business-container",
            "sa-mono",
            "@media (min-width: 500px)",  # 80mm printer
            "@media (max-width: 400px)",  # 58mm printer
            "@media print",
            "font-family: 'Courier New'",
            "direction: rtl",
            "direction: ltr"
        ]
        
        for element in css_elements:
            if element in content:
                self.log_test(f"CSS contains: {element}", "PASS")
            else:
                self.log_test(f"CSS missing: {element}", "FAIL")
    
    def test_manifest_configuration(self):
        """Test manifest configuration"""
        manifest_file = os.path.join(self.module_path, "__manifest__.py")
        
        if not os.path.exists(manifest_file):
            self.log_test("Manifest configuration", "FAIL", "Manifest file not found")
            return
        
        with open(manifest_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        manifest_elements = [
            "'point_of_sale'",
            "saudi_receipt_qr.js",
            "custom_order_receipt.xml",
            "receipt_header.xml",
            "custom_pos_receipts.css"
        ]
        
        for element in manifest_elements:
            if element in content:
                self.log_test(f"Manifest contains: {element}", "PASS")
            else:
                self.log_test(f"Manifest missing: {element}", "FAIL")
    
    def run_all_tests(self):
        """Run all tests"""
        print("=" * 60)
        print("Saudi Arabian Receipt Implementation Test")
        print("=" * 60)
        print()
        
        self.test_file_structure()
        print()
        self.test_receipt_header_content()
        print()
        self.test_transaction_details()
        print()
        self.test_products_table_structure()
        print()
        self.test_totals_section()
        print()
        self.test_qr_code_implementation()
        print()
        self.test_css_thermal_optimization()
        print()
        self.test_manifest_configuration()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        passed = len([r for r in self.test_results if r["status"] == "PASS"])
        failed = len([r for r in self.test_results if r["status"] == "FAIL"])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if failed > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed == 0

if __name__ == "__main__":
    tester = SaudiReceiptTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
